#!/usr/bin/env python3
"""
Build script for FX AI MCP Server.

This script provides various build and packaging options:
- Source distribution (sdist)
- Wheel distribution (bdist_wheel)
- Standalone executable (PyInstaller)
- Docker image
- Clean build artifacts
"""

import os
import sys
import shutil
import subprocess
import argparse
from pathlib import Path


def run_command(cmd, cwd=None):
    """Run a command and return the result."""
    print(f"Running: {' '.join(cmd)}")
    try:
        result = subprocess.run(
            cmd, 
            cwd=cwd, 
            check=True, 
            capture_output=True, 
            text=True
        )
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error: {e}")
        if e.stdout:
            print(f"STDOUT: {e.stdout}")
        if e.stderr:
            print(f"STDERR: {e.stderr}")
        return False


def clean_build():
    """Clean build artifacts."""
    print("🧹 Cleaning build artifacts...")
    
    # Directories to clean
    clean_dirs = [
        "build",
        "dist", 
        "*.egg-info",
        "__pycache__",
        ".pytest_cache",
        ".mypy_cache",
        "src/**/__pycache__",
        "tests/**/__pycache__",
    ]
    
    for pattern in clean_dirs:
        for path in Path(".").glob(pattern):
            if path.is_dir():
                print(f"Removing directory: {path}")
                shutil.rmtree(path)
            elif path.is_file():
                print(f"Removing file: {path}")
                path.unlink()
    
    print("✅ Clean completed")


def build_wheel():
    """Build wheel distribution."""
    print("🔧 Building wheel distribution...")
    
    # Install build dependencies
    if not run_command([sys.executable, "-m", "pip", "install", "build", "wheel"]):
        return False
    
    # Build wheel
    if not run_command([sys.executable, "-m", "build", "--wheel"]):
        return False
    
    print("✅ Wheel build completed")
    return True


def build_sdist():
    """Build source distribution."""
    print("📦 Building source distribution...")
    
    # Install build dependencies
    if not run_command([sys.executable, "-m", "pip", "install", "build"]):
        return False
    
    # Build sdist
    if not run_command([sys.executable, "-m", "build", "--sdist"]):
        return False
    
    print("✅ Source distribution build completed")
    return True


def build_all():
    """Build both wheel and source distributions."""
    print("🚀 Building all distributions...")
    
    # Install build dependencies
    if not run_command([sys.executable, "-m", "pip", "install", "build", "wheel"]):
        return False
    
    # Build both
    if not run_command([sys.executable, "-m", "build"]):
        return False
    
    print("✅ All distributions build completed")
    return True


def build_executable():
    """Build standalone executable using PyInstaller."""
    print("⚡ Building standalone executable...")
    
    # Install PyInstaller
    if not run_command([sys.executable, "-m", "pip", "install", "pyinstaller"]):
        return False
    
    # Create PyInstaller spec
    spec_content = '''
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['run_server.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('src/fx_ai_mcp', 'fx_ai_mcp'),
        ('.env.example', '.'),
        ('README.md', '.'),
        ('LICENSE', '.'),
    ],
    hiddenimports=[
        'fx_ai_mcp',
        'fx_ai_mcp.server',
        'fx_ai_mcp.config',
        'fx_ai_mcp.models',
        'mcp',
        'httpx',
        'pydantic',
        'dotenv',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='fx-ai-mcp-server',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open('fx-ai-mcp-server.spec', 'w') as f:
        f.write(spec_content.strip())
    
    # Build executable
    if not run_command([sys.executable, "-m", "PyInstaller", "fx-ai-mcp-server.spec", "--clean"]):
        return False
    
    print("✅ Executable build completed")
    print("📁 Executable location: dist/fx-ai-mcp-server.exe (Windows) or dist/fx-ai-mcp-server (Unix)")
    return True


def create_docker_files():
    """Create Docker files for containerization."""
    print("🐳 Creating Docker files...")
    
    # Dockerfile
    dockerfile_content = '''FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \\
    gcc \\
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy source code
COPY src/ src/
COPY pyproject.toml .
COPY README.md .
COPY LICENSE .
COPY .env.example .

# Install the package
RUN pip install -e .

# Create non-root user
RUN useradd --create-home --shell /bin/bash app
USER app

# Expose port (if needed for future HTTP interface)
EXPOSE 8000

# Set environment variables
ENV PYTHONPATH=/app/src
ENV FX_DEBUG=false

# Default command
CMD ["python", "-m", "fx_ai_mcp.server"]
'''
    
    with open('Dockerfile', 'w') as f:
        f.write(dockerfile_content.strip())
    
    # Docker Compose
    compose_content = '''version: '3.8'

services:
  fx-ai-mcp-server:
    build: .
    container_name: fx-ai-mcp-server
    environment:
      - FX_OCR_AUTHORIZATION=${FX_OCR_AUTHORIZATION}
      - FX_CHARACTER_AUTHORIZATION=${FX_CHARACTER_AUTHORIZATION}
      - FX_SUMMARY_AUTHORIZATION=${FX_SUMMARY_AUTHORIZATION}
      - FX_DEBUG=${FX_DEBUG:-false}
    volumes:
      - ./.env:/app/.env:ro
    restart: unless-stopped
    stdin_open: true
    tty: true
'''
    
    with open('docker-compose.yml', 'w') as f:
        f.write(compose_content.strip())
    
    # .dockerignore
    dockerignore_content = '''__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

.DS_Store
.vscode
.idea

build/
dist/
*.egg-info/
.env
'''
    
    with open('.dockerignore', 'w') as f:
        f.write(dockerignore_content.strip())
    
    print("✅ Docker files created")
    print("📁 Files created: Dockerfile, docker-compose.yml, .dockerignore")
    return True


def build_docker():
    """Build Docker image."""
    print("🐳 Building Docker image...")
    
    if not run_command(["docker", "build", "-t", "fx-ai-mcp-server:latest", "."]):
        return False
    
    print("✅ Docker image build completed")
    print("🚀 Run with: docker run -it --env-file .env fx-ai-mcp-server:latest")
    return True


def show_dist_info():
    """Show information about built distributions."""
    print("\n📦 Distribution Information:")
    print("=" * 50)
    
    dist_dir = Path("dist")
    if not dist_dir.exists():
        print("No distributions found. Run build first.")
        return
    
    for file in dist_dir.iterdir():
        if file.is_file():
            size = file.stat().st_size
            size_mb = size / (1024 * 1024)
            print(f"📁 {file.name}")
            print(f"   Size: {size:,} bytes ({size_mb:.2f} MB)")
            print(f"   Path: {file.absolute()}")
            print()


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Build FX AI MCP Server")
    parser.add_argument("command", choices=[
        "clean", "wheel", "sdist", "all", "exe", "docker-files", "docker", "info"
    ], help="Build command to execute")
    
    args = parser.parse_args()
    
    print("🔨 FX AI MCP Server Build Tool")
    print("=" * 40)
    
    success = True
    
    if args.command == "clean":
        clean_build()
    elif args.command == "wheel":
        success = build_wheel()
    elif args.command == "sdist":
        success = build_sdist()
    elif args.command == "all":
        success = build_all()
    elif args.command == "exe":
        success = build_executable()
    elif args.command == "docker-files":
        success = create_docker_files()
    elif args.command == "docker":
        success = build_docker()
    elif args.command == "info":
        show_dist_info()
    
    if success:
        show_dist_info()
        print("\n✅ Build completed successfully!")
    else:
        print("\n❌ Build failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
