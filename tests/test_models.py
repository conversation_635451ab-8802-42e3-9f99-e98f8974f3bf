"""Tests for data models."""

import pytest
from pydantic import ValidationError

from fx_ai_mcp.models import (
    Location,
    WordResult,
    OCRResponse,
    ChineseCharacterData,
    ChineseCharacterResponse,
    NoteSummaryData,
    NoteSummaryResponse,
    OCRRequest,
    ChineseCharacterRequest,
    NoteSummaryRequest,
    APIConfig,
    ServerConfig,
)


class TestLocation:
    """Test Location model."""
    
    def test_valid_location(self):
        """Test valid location creation."""
        location = Location(top=10, left=20, width=100, height=50)
        assert location.top == 10
        assert location.left == 20
        assert location.width == 100
        assert location.height == 50
    
    def test_location_validation(self):
        """Test location validation."""
        with pytest.raises(ValidationError):
            Location(top="invalid", left=20, width=100, height=50)


class TestWordResult:
    """Test WordResult model."""
    
    def test_valid_word_result(self):
        """Test valid word result creation."""
        location = Location(top=10, left=20, width=100, height=50)
        word_result = WordResult(words="test", location=location)
        assert word_result.words == "test"
        assert word_result.location == location


class TestOCRModels:
    """Test OCR-related models."""
    
    def test_ocr_request(self):
        """Test OCR request model."""
        request = OCRRequest(image="base64_image_data")
        assert request.image == "base64_image_data"
    
    def test_ocr_response(self):
        """Test OCR response model."""
        response = OCRResponse(code=0, msg="success")
        assert response.code == 0
        assert response.msg == "success"
        assert response.data is None


class TestChineseCharacterModels:
    """Test Chinese character models."""
    
    def test_character_request(self):
        """Test character request model."""
        request = ChineseCharacterRequest(word="你")
        assert request.word == "你"
    
    def test_character_data(self):
        """Test character data model."""
        data = ChineseCharacterData(
            name="你",
            struct="左右结构",
            pinyin=["nǐ"],
            voice=["http://example.com/voice.mp3"],
            definition=["代词，称对方"]
        )
        assert data.name == "你"
        assert data.struct == "左右结构"
        assert data.pinyin == ["nǐ"]
        assert data.voice == ["http://example.com/voice.mp3"]
        assert data.definition == ["代词，称对方"]
    
    def test_character_response(self):
        """Test character response model."""
        response = ChineseCharacterResponse(code=0, msg="success")
        assert response.code == 0
        assert response.msg == "success"
        assert response.data is None


class TestNoteSummaryModels:
    """Test note summary models."""
    
    def test_summary_request(self):
        """Test summary request model."""
        request = NoteSummaryRequest(content="test content", stream=True)
        assert request.content == "test content"
        assert request.stream is True
    
    def test_summary_request_default_stream(self):
        """Test summary request with default stream value."""
        request = NoteSummaryRequest(content="test content")
        assert request.content == "test content"
        assert request.stream is False
    
    def test_summary_data(self):
        """Test summary data model."""
        data = NoteSummaryData(content="summary content", is_end="1")
        assert data.content == "summary content"
        assert data.is_end == "1"
    
    def test_summary_response(self):
        """Test summary response model."""
        response = NoteSummaryResponse(
            msg="success",
            logId="12345",
            status=0
        )
        assert response.msg == "success"
        assert response.logId == "12345"
        assert response.status == 0
        assert response.data is None


class TestConfigModels:
    """Test configuration models."""
    
    def test_api_config(self):
        """Test API config model."""
        config = APIConfig(
            base_url="https://api.example.com",
            authorization="token123",
            timeout=30,
            max_retries=3
        )
        assert config.base_url == "https://api.example.com"
        assert config.authorization == "token123"
        assert config.timeout == 30
        assert config.max_retries == 3
    
    def test_api_config_defaults(self):
        """Test API config with default values."""
        config = APIConfig(
            base_url="https://api.example.com",
            authorization="token123"
        )
        assert config.timeout == 30
        assert config.max_retries == 3
    
    def test_server_config(self):
        """Test server config model."""
        ocr_config = APIConfig(
            base_url="https://ocr.example.com",
            authorization="ocr_token"
        )
        character_config = APIConfig(
            base_url="https://char.example.com",
            authorization="char_token"
        )
        summary_config = APIConfig(
            base_url="https://summary.example.com",
            authorization="summary_token"
        )
        
        server_config = ServerConfig(
            ocr_config=ocr_config,
            character_config=character_config,
            summary_config=summary_config,
            debug=True
        )
        
        assert server_config.ocr_config == ocr_config
        assert server_config.character_config == character_config
        assert server_config.summary_config == summary_config
        assert server_config.debug is True
    
    def test_server_config_default_debug(self):
        """Test server config with default debug value."""
        ocr_config = APIConfig(
            base_url="https://ocr.example.com",
            authorization="ocr_token"
        )
        character_config = APIConfig(
            base_url="https://char.example.com",
            authorization="char_token"
        )
        summary_config = APIConfig(
            base_url="https://summary.example.com",
            authorization="summary_token"
        )
        
        server_config = ServerConfig(
            ocr_config=ocr_config,
            character_config=character_config,
            summary_config=summary_config
        )
        
        assert server_config.debug is False
