# Include documentation
include README.md
include QUICKSTART.md
include LICENSE
include CHANGELOG.md

# Include configuration files
include .env.example
include mcp_config_example.json
include requirements.txt

# Include build and project files
include pyproject.toml
include MANIFEST.in

# Include examples
recursive-include examples *.py
recursive-include examples *.md

# Include tests
recursive-include tests *.py

# Include source code
recursive-include src *.py

# Exclude build artifacts
global-exclude *.pyc
global-exclude *.pyo
global-exclude *.pyd
global-exclude __pycache__
global-exclude .pytest_cache
global-exclude .mypy_cache
global-exclude *.egg-info
global-exclude build
global-exclude dist

# Exclude development files
exclude .gitignore
exclude .pre-commit-config.yaml
exclude build.py
exclude fx-ai-mcp-server.spec
exclude Dockerfile
exclude docker-compose.yml
exclude .dockerignore

# Exclude environment files
exclude .env
