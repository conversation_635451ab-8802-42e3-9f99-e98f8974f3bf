# FX AI MCP Server - Quick Start Guide

This guide will help you get the FX AI MCP Server up and running quickly.

## Prerequisites

- Python 3.8 or higher
- pip package manager
- Valid authorization tokens for FX AI services

## Installation

### Option 1: Install from Source (Recommended)

```bash
# Clone or download the project
cd fx_mcp_server

# Install dependencies
pip install -r requirements.txt

# Or install in development mode
pip install -e .
```

### Option 2: Direct Installation

```bash
# Install dependencies directly
pip install mcp httpx pydantic python-dotenv typing-extensions
```

## Configuration

1. **Copy the environment template:**
   ```bash
   cp .env.example .env
   ```

2. **Edit `.env` with your actual tokens:**
   ```env
   FX_OCR_AUTHORIZATION=your_actual_ocr_token_here
   FX_CHARACTER_AUTHORIZATION=your_actual_character_token_here
   FX_SUMMARY_AUTHORIZATION=your_actual_summary_token_here
   ```

## Running the Server

### Method 1: Using the convenience script

```bash
python run_server.py
```

### Method 2: Direct execution

```bash
python -m fx_ai_mcp.server
```

### Method 3: Using the installed command (if installed with pip)

```bash
fx-ai-mcp-server
```

## Testing the Installation

### Test 1: Basic Import Test

```bash
python -c "import sys; sys.path.insert(0, 'src'); from fx_ai_mcp import config; print('✓ Import successful!')"
```

### Test 2: Configuration Test

```bash
python -c "
import sys; sys.path.insert(0, 'src')
from fx_ai_mcp.config import config
try:
    config.validate_config()
    print('✓ Configuration valid!')
except Exception as e:
    print(f'✗ Configuration error: {e}')
"
```

### Test 3: Run Examples

```bash
# Test direct client usage
python examples/direct_client_usage.py

# Test MCP client usage (requires MCP client setup)
python examples/basic_usage.py
```

## Using with MCP Clients

### Claude Desktop Configuration

Add to your Claude Desktop configuration file:

```json
{
  "mcpServers": {
    "fx-ai": {
      "command": "python",
      "args": ["/path/to/fx_mcp_server/run_server.py"],
      "env": {
        "FX_OCR_AUTHORIZATION": "your_ocr_token",
        "FX_CHARACTER_AUTHORIZATION": "your_character_token", 
        "FX_SUMMARY_AUTHORIZATION": "your_summary_token"
      }
    }
  }
}
```

### Other MCP Clients

Use the configuration template in `mcp_config_example.json` and adapt it for your specific MCP client.

## Available Tools

Once running, the server provides three tools:

1. **`ocr_image`** - Extract text from images
   - Input: `image` (base64 encoded image)
   - Output: Recognized text with locations

2. **`query_chinese_character`** - Query Chinese character information
   - Input: `word` (Chinese character)
   - Output: Pinyin, structure, definition, voice URL

3. **`summarize_note`** - Summarize text content
   - Input: `content` (text to summarize), `stream` (optional boolean)
   - Output: Intelligent summary

## Troubleshooting

### Common Issues

1. **"No module named 'fx_ai_mcp'"**
   - Make sure you're in the correct directory
   - Try: `python -c "import sys; sys.path.insert(0, 'src'); import fx_ai_mcp"`

2. **"Missing required configuration"**
   - Check your `.env` file exists and has the correct tokens
   - Verify token format and permissions

3. **"Connection timeout"**
   - Check internet connectivity
   - Verify API endpoints are accessible
   - Try increasing timeout values in configuration

4. **"Authentication failed"**
   - Verify your authorization tokens are correct
   - Check token expiration dates
   - Ensure tokens have proper permissions

### Debug Mode

Enable debug logging:

```bash
# In .env file
FX_DEBUG=true

# Or as environment variable
export FX_DEBUG=true
python run_server.py
```

### Getting Help

- Check the full documentation in `README.md`
- Review example code in the `examples/` directory
- Check the issue tracker for known problems
- Enable debug mode for detailed error messages

## Next Steps

1. **Integrate with your MCP client** - Follow your client's documentation for adding MCP servers
2. **Customize configuration** - Adjust timeouts, retries, and other settings as needed
3. **Explore examples** - Run the provided examples to understand usage patterns
4. **Build your application** - Use the tools in your own applications and workflows

## Project Structure

```
fx_mcp_server/
├── src/fx_ai_mcp/          # Main package
│   ├── server.py           # MCP server implementation
│   ├── config.py           # Configuration management
│   ├── models.py           # Data models
│   └── __init__.py         # Package initialization
├── examples/               # Usage examples
├── tests/                  # Test files
├── .env.example           # Environment template
├── requirements.txt       # Dependencies
├── pyproject.toml         # Project configuration
├── run_server.py          # Convenience runner
└── README.md              # Full documentation
```

Happy coding with FX AI MCP Server! 🚀
