#!/bin/bash
# Linux/Mac shell script for packaging FX AI MCP Server

set -e  # Exit on any error

echo "========================================"
echo "FX AI MCP Server - Package Builder"
echo "========================================"

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "Error: Python 3 is not installed or not in PATH"
    exit 1
fi

# Create virtual environment if it doesn't exist
if [ ! -d ".venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv .venv
fi

# Activate virtual environment
echo "Activating virtual environment..."
source .venv/bin/activate

# Upgrade pip and install build tools
echo "Installing build dependencies..."
python -m pip install --upgrade pip
python -m pip install build wheel setuptools

# Clean previous builds
echo "Cleaning previous builds..."
rm -rf build dist *.egg-info

# Build the package
echo "Building package..."
python -m build

# Show results
echo ""
echo "========================================"
echo "Build completed!"
echo "========================================"
echo ""
echo "Built packages:"
ls -la dist/
echo ""
echo "To install the package:"
echo "  pip install dist/fx_ai_mcp_server-1.0.0-py3-none-any.whl"
echo ""
echo "To upload to PyPI (if configured):"
echo "  python -m twine upload dist/*"
echo ""

# Make the script executable
chmod +x package.sh
