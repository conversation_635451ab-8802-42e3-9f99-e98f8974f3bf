@echo off
REM Windows batch script for packaging FX AI MCP Server

echo ========================================
echo FX AI MCP Server - Package Builder
echo ========================================

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    pause
    exit /b 1
)

REM Create virtual environment if it doesn't exist
if not exist ".venv" (
    echo Creating virtual environment...
    python -m venv .venv
)

REM Activate virtual environment
echo Activating virtual environment...
call .venv\Scripts\activate.bat

REM Upgrade pip and install build tools
echo Installing build dependencies...
python -m pip install --upgrade pip
python -m pip install build wheel setuptools

REM Clean previous builds
echo Cleaning previous builds...
if exist "build" rmdir /s /q build
if exist "dist" rmdir /s /q dist
for /d %%i in (*.egg-info) do rmdir /s /q "%%i"

REM Build the package
echo Building package...
python -m build

REM Show results
echo.
echo ========================================
echo Build completed!
echo ========================================
echo.
echo Built packages:
dir dist
echo.
echo To install the package:
echo   pip install dist\fx_ai_mcp_server-1.0.0-py3-none-any.whl
echo.
echo To upload to PyPI (if configured):
echo   python -m twine upload dist/*
echo.

pause
