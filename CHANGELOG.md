# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.0.0] - 2025-07-03

### Added
- Initial release of FX AI MCP Server
- OCR service integration for image text recognition
- Chinese character query service with pinyin, structure, and definitions
- Note summary service with streaming support
- Full MCP (Model Context Protocol) implementation
- Async HTTP client with retry mechanisms
- Environment-based configuration management
- Type-safe implementation with Pydantic models
- Comprehensive error handling
- Docker support
- PyInstaller executable building
- Complete documentation and examples
- Test suite for data models

### Features
- **OCR Image Tool**: Extract text from base64 encoded images
- **Chinese Character Query Tool**: Get detailed character information
- **Note Summary Tool**: Generate intelligent summaries with optional streaming
- **Configuration Management**: Environment variable based configuration
- **Error Handling**: Comprehensive error handling with retries
- **Type Safety**: Full type hints and Pydantic validation
- **Async Support**: Modern async/await patterns
- **Multiple Packaging Options**: Wheel, source, executable, and Docker

### Technical Details
- Python 3.8+ support
- MCP protocol compliance
- HTTP/HTTPS API integration
- Base64 image processing
- Streaming response support
- Configurable timeouts and retries
- Debug logging support

### Documentation
- Complete README with API reference
- Quick start guide
- Usage examples
- Docker deployment guide
- Development setup instructions
- MCP client configuration examples
