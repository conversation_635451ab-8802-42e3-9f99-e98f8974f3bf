"""Configuration management for FX AI MCP Server."""

import os
from typing import Optional
from dotenv import load_dotenv
from .models import APIConfig, ServerConfig

# Load environment variables
load_dotenv()


class Config:
    """Configuration manager for the FX AI MCP Server."""
    
    def __init__(self):
        """Initialize configuration from environment variables."""
        self._load_config()
    
    def _load_config(self) -> None:
        """Load configuration from environment variables."""
        # OCR Service Configuration
        self.ocr_base_url = os.getenv(
            "FX_OCR_BASE_URL", 
            "https://ivs.chinamobiledevice.com:11443"
        )
        self.ocr_authorization = os.getenv("FX_OCR_AUTHORIZATION", "")
        self.ocr_timeout = int(os.getenv("FX_OCR_TIMEOUT", "30"))
        self.ocr_max_retries = int(os.getenv("FX_OCR_MAX_RETRIES", "3"))
        
        # Chinese Character Query Service Configuration
        self.character_base_url = os.getenv(
            "FX_CHARACTER_BASE_URL", 
            "https://ivs.chinamobiledevice.com:11443"
        )
        self.character_authorization = os.getenv("FX_CHARACTER_AUTHORIZATION", "")
        self.character_timeout = int(os.getenv("FX_CHARACTER_TIMEOUT", "30"))
        self.character_max_retries = int(os.getenv("FX_CHARACTER_MAX_RETRIES", "3"))
        
        # Note Summary Service Configuration
        self.summary_base_url = os.getenv(
            "FX_SUMMARY_BASE_URL", 
            "https://ivs.chinamobiledevice.com:11443"
        )
        self.summary_authorization = os.getenv("FX_SUMMARY_AUTHORIZATION", "")
        self.summary_timeout = int(os.getenv("FX_SUMMARY_TIMEOUT", "60"))  # Longer timeout for summary
        self.summary_max_retries = int(os.getenv("FX_SUMMARY_MAX_RETRIES", "3"))
        
        # General Configuration
        self.debug = os.getenv("FX_DEBUG", "false").lower() == "true"
        
    def get_server_config(self) -> ServerConfig:
        """Get the complete server configuration."""
        return ServerConfig(
            ocr_config=APIConfig(
                base_url=self.ocr_base_url,
                authorization=self.ocr_authorization,
                timeout=self.ocr_timeout,
                max_retries=self.ocr_max_retries
            ),
            character_config=APIConfig(
                base_url=self.character_base_url,
                authorization=self.character_authorization,
                timeout=self.character_timeout,
                max_retries=self.character_max_retries
            ),
            summary_config=APIConfig(
                base_url=self.summary_base_url,
                authorization=self.summary_authorization,
                timeout=self.summary_timeout,
                max_retries=self.summary_max_retries
            ),
            debug=self.debug
        )
    
    def validate_config(self) -> bool:
        """Validate that all required configuration is present."""
        required_configs = [
            ("OCR Authorization", self.ocr_authorization),
            ("Character Authorization", self.character_authorization),
            ("Summary Authorization", self.summary_authorization),
        ]
        
        missing_configs = []
        for name, value in required_configs:
            if not value:
                missing_configs.append(name)
        
        if missing_configs:
            raise ValueError(
                f"Missing required configuration: {', '.join(missing_configs)}. "
                "Please set the appropriate environment variables."
            )
        
        return True


# Global configuration instance
config = Config()
