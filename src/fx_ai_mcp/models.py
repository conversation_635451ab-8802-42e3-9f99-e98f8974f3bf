"""Data models for FX AI MCP Server."""

from typing import List, Optional, Union
from pydantic import BaseModel, Field


class Location(BaseModel):
    """Location information for OCR results."""
    top: int = Field(..., description="Top coordinate")
    left: int = Field(..., description="Left coordinate")
    width: int = Field(..., description="Width of the text region")
    height: int = Field(..., description="Height of the text region")


class WordResult(BaseModel):
    """Individual word result from OCR."""
    words: str = Field(..., description="Recognized text")
    location: Location = Field(..., description="Location of the text")


class OCRResponse(BaseModel):
    """Response model for OCR service."""
    code: int = Field(..., description="Response code")
    data: Optional[dict] = Field(None, description="Response data")
    msg: str = Field("", description="Response message")
    
    class Data(BaseModel):
        words_result: List[WordResult] = Field(..., description="List of recognized words")
        log_id: str = Field(..., description="Log ID for tracking")
        words_result_num: int = Field(..., description="Number of recognized words")


class ChineseCharacterData(BaseModel):
    """Chinese character information."""
    name: str = Field(..., description="The Chinese character")
    struct: str = Field(..., description="Character structure (e.g., 左右结构)")
    pinyin: List[str] = Field(..., description="Pinyin pronunciations")
    voice: List[str] = Field(..., description="Voice file URLs")
    definition: List[str] = Field(..., description="Character definitions")


class ChineseCharacterResponse(BaseModel):
    """Response model for Chinese character query service."""
    code: int = Field(..., description="Response code")
    data: Optional[ChineseCharacterData] = Field(None, description="Character data")
    msg: str = Field("", description="Response message")


class NoteSummaryData(BaseModel):
    """Note summary data."""
    content: str = Field(..., description="Summary content")
    is_end: Optional[str] = Field(None, description="Stream end indicator")


class NoteSummaryResponse(BaseModel):
    """Response model for note summary service."""
    msg: str = Field("", description="Response message")
    data: Optional[NoteSummaryData] = Field(None, description="Summary data")
    logId: str = Field(..., description="Log ID for tracking")
    status: int = Field(..., description="Response status")


# Request models
class OCRRequest(BaseModel):
    """Request model for OCR service."""
    image: str = Field(..., description="Base64 encoded image")


class ChineseCharacterRequest(BaseModel):
    """Request model for Chinese character query."""
    word: str = Field(..., description="Chinese character to query")


class NoteSummaryRequest(BaseModel):
    """Request model for note summary service."""
    content: str = Field(..., description="Content to summarize")
    stream: bool = Field(False, description="Enable streaming response")


# Configuration models
class APIConfig(BaseModel):
    """API configuration."""
    base_url: str = Field(..., description="Base URL for the API")
    authorization: str = Field(..., description="Authorization token")
    timeout: int = Field(30, description="Request timeout in seconds")
    max_retries: int = Field(3, description="Maximum number of retries")


class ServerConfig(BaseModel):
    """Server configuration."""
    ocr_config: APIConfig = Field(..., description="OCR service configuration")
    character_config: APIConfig = Field(..., description="Character query service configuration")
    summary_config: APIConfig = Field(..., description="Summary service configuration")
    debug: bool = Field(False, description="Enable debug mode")
