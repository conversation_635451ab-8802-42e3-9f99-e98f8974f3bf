"""FX AI MCP Server - OCR, Chinese Character Query, and Note Summary Services."""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional, AsyncGenerator
import httpx
from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    CallToolRequest,
    CallToolResult,
    ListToolsRequest,
    ListToolsResult,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
)

from .config import config
from .models import (
    OCRRequest,
    OCRResponse,
    ChineseCharacterRequest,
    ChineseCharacterResponse,
    NoteSummaryRequest,
    NoteSummaryResponse,
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize server
server = Server("fx-ai-mcp-server")


class FXAIClient:
    """HTTP client for FX AI services."""

    def __init__(self):
        """Initialize the HTTP client."""
        self.config = config.get_server_config()
        self.client = httpx.AsyncClient()

    async def __aenter__(self):
        """Async context manager entry."""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.client.aclose()

    async def ocr_image(self, image_base64: str) -> OCRResponse:
        """Perform OCR on an image."""
        url = f"{self.config.ocr_config.base_url}/open-api/ai/v1/ocr"
        headers = {
            "Authorization": self.config.ocr_config.authorization,
            "Content-Type": "application/json"
        }
        data = {"image": image_base64}

        try:
            response = await self.client.post(
                url,
                headers=headers,
                json=data,
                timeout=self.config.ocr_config.timeout
            )
            response.raise_for_status()
            return OCRResponse(**response.json())
        except httpx.HTTPError as e:
            logger.error(f"OCR request failed: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error in OCR: {e}")
            raise

    async def query_chinese_character(self, word: str) -> ChineseCharacterResponse:
        """Query information about a Chinese character."""
        url = f"{self.config.character_config.base_url}/device-api/ai/v1/word-query"
        headers = {
            "Authorization": self.config.character_config.authorization,
            "Content-Type": "application/json"
        }
        data = {"word": word}

        try:
            response = await self.client.post(
                url,
                headers=headers,
                json=data,
                timeout=self.config.character_config.timeout
            )
            response.raise_for_status()
            return ChineseCharacterResponse(**response.json())
        except httpx.HTTPError as e:
            logger.error(f"Character query request failed: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error in character query: {e}")
            raise

    async def summarize_note(self, content: str, stream: bool = False) -> NoteSummaryResponse:
        """Summarize note content."""
        url = f"{self.config.summary_config.base_url}/device-api/ai/v1/note-summary"
        headers = {
            "Authorization": self.config.summary_config.authorization,
            "Content-Type": "application/json"
        }
        data = {"content": content, "stream": stream}

        try:
            if stream:
                # Handle streaming response
                async with self.client.stream(
                    "POST",
                    url,
                    headers=headers,
                    json=data,
                    timeout=self.config.summary_config.timeout
                ) as response:
                    response.raise_for_status()
                    full_content = ""
                    async for line in response.aiter_lines():
                        if line.startswith("data:"):
                            try:
                                data_str = line[5:].strip()  # Remove "data:" prefix
                                if data_str:
                                    chunk_data = json.loads(data_str)
                                    if chunk_data.get("data", {}).get("content"):
                                        full_content += chunk_data["data"]["content"]
                            except json.JSONDecodeError:
                                continue

                    # Return the complete response
                    from .models import NoteSummaryData
                    return NoteSummaryResponse(
                        msg="",
                        data=NoteSummaryData(content=full_content),
                        logId="stream_response",
                        status=0
                    )
            else:
                # Handle non-streaming response
                response = await self.client.post(
                    url,
                    headers=headers,
                    json=data,
                    timeout=self.config.summary_config.timeout
                )
                response.raise_for_status()
                return NoteSummaryResponse(**response.json())
        except httpx.HTTPError as e:
            logger.error(f"Note summary request failed: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error in note summary: {e}")
            raise


# Global client instance
fx_client = FXAIClient()


@server.list_tools()
async def list_tools() -> ListToolsResult:
    """List available tools."""
    return ListToolsResult(
        tools=[
            Tool(
                name="ocr_image",
                description="Perform OCR (Optical Character Recognition) on an image to extract text",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "image": {
                            "type": "string",
                            "description": "Base64 encoded image data"
                        }
                    },
                    "required": ["image"]
                }
            ),
            Tool(
                name="query_chinese_character",
                description="Query detailed information about a Chinese character including pinyin, structure, and definition",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "word": {
                            "type": "string",
                            "description": "Chinese character to query"
                        }
                    },
                    "required": ["word"]
                }
            ),
            Tool(
                name="summarize_note",
                description="Generate a summary of note content with optional streaming support",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "content": {
                            "type": "string",
                            "description": "Content to summarize"
                        },
                        "stream": {
                            "type": "boolean",
                            "description": "Enable streaming response (default: false)",
                            "default": False
                        }
                    },
                    "required": ["content"]
                }
            )
        ]
    )


@server.call_tool()
async def call_tool(name: str, arguments: Dict[str, Any]) -> CallToolResult:
    """Handle tool calls."""
    try:
        if name == "ocr_image":
            # Validate arguments
            if "image" not in arguments:
                raise ValueError("Missing required argument: image")

            image_base64 = arguments["image"]

            # Perform OCR
            async with FXAIClient() as client:
                result = await client.ocr_image(image_base64)

            # Format response
            if result.code == 0 and result.data:
                words_list = []
                for word_result in result.data.get("words_result", []):
                    words_list.append({
                        "text": word_result.get("words", ""),
                        "location": word_result.get("location", {})
                    })

                response_text = f"OCR Results:\n"
                response_text += f"Total words found: {result.data.get('words_result_num', 0)}\n\n"

                for i, word in enumerate(words_list, 1):
                    response_text += f"{i}. Text: '{word['text']}'\n"
                    loc = word['location']
                    response_text += f"   Location: ({loc.get('left', 0)}, {loc.get('top', 0)}) "
                    response_text += f"Size: {loc.get('width', 0)}x{loc.get('height', 0)}\n\n"

                return CallToolResult(
                    content=[TextContent(type="text", text=response_text)]
                )
            else:
                return CallToolResult(
                    content=[TextContent(
                        type="text",
                        text=f"OCR failed: {result.msg or 'Unknown error'}"
                    )]
                )

        elif name == "query_chinese_character":
            # Validate arguments
            if "word" not in arguments:
                raise ValueError("Missing required argument: word")

            word = arguments["word"]

            # Query character
            async with FXAIClient() as client:
                result = await client.query_chinese_character(word)

            # Format response
            if result.code == 0 and result.data:
                data = result.data
                response_text = f"Chinese Character Information: '{data.name}'\n\n"
                response_text += f"Structure: {data.struct}\n"
                response_text += f"Pinyin: {', '.join(data.pinyin)}\n"
                response_text += f"Definition: {'; '.join(data.definition)}\n"

                if data.voice:
                    response_text += f"Voice URLs: {', '.join(data.voice)}\n"

                return CallToolResult(
                    content=[TextContent(type="text", text=response_text)]
                )
            else:
                return CallToolResult(
                    content=[TextContent(
                        type="text",
                        text=f"Character query failed: {result.msg or 'Unknown error'}"
                    )]
                )

        elif name == "summarize_note":
            # Validate arguments
            if "content" not in arguments:
                raise ValueError("Missing required argument: content")

            content = arguments["content"]
            stream = arguments.get("stream", False)

            # Summarize note
            async with FXAIClient() as client:
                result = await client.summarize_note(content, stream)

            # Format response
            if result.status == 0 and result.data:
                summary_content = result.data.get("content", "")
                response_text = f"Note Summary:\n\n{summary_content}"

                return CallToolResult(
                    content=[TextContent(type="text", text=response_text)]
                )
            else:
                return CallToolResult(
                    content=[TextContent(
                        type="text",
                        text=f"Note summary failed: {result.msg or 'Unknown error'}"
                    )]
                )

        else:
            raise ValueError(f"Unknown tool: {name}")

    except Exception as e:
        logger.error(f"Tool call failed: {e}")
        return CallToolResult(
            content=[TextContent(
                type="text",
                text=f"Error: {str(e)}"
            )]
        )


async def main():
    """Main function to run the MCP server."""
    try:
        # Validate configuration
        config.validate_config()
        logger.info("Configuration validated successfully")

        # Run the server
        logger.info("Starting FX AI MCP Server...")
        async with stdio_server() as (read_stream, write_stream):
            await server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="fx-ai-mcp-server",
                    server_version="1.0.0",
                    capabilities=server.get_capabilities(
                        notification_options=None,
                        experimental_capabilities=None,
                    ),
                ),
            )
    except Exception as e:
        logger.error(f"Server failed to start: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())