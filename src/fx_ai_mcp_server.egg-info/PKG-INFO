Metadata-Version: 2.1
Name: fx-ai-mcp-server
Version: 1.0.0
Summary: MCP Server for FX AI Services (OCR, Chinese Character Query, Note Summary)
Author-email: FX AI Team <<EMAIL>>
License: MIT
Project-URL: Homepage, https://github.com/fx-ai/fx-mcp-server
Project-URL: Repository, https://github.com/fx-ai/fx-mcp-server
Project-URL: Documentation, https://github.com/fx-ai/fx-mcp-server#readme
Project-URL: Issues, https://github.com/fx-ai/fx-mcp-server/issues
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Requires-Python: >=3.8
Description-Content-Type: text/markdown
Provides-Extra: dev
License-File: LICENSE

# FX AI MCP Server

A Model Context Protocol (MCP) server that provides access to FX AI services including OCR (Optical Character Recognition), Chinese Character Query, and Note Summary capabilities.

## Features

- **OCR Service**: Extract text from images using advanced optical character recognition
- **Chinese Character Query**: Get detailed information about Chinese characters including pinyin, structure, and definitions
- **Note Summary**: Generate intelligent summaries of text content with optional streaming support
- **Async Support**: Built with modern async/await patterns for optimal performance
- **Type Safety**: Full type hints and Pydantic models for data validation
- **Error Handling**: Comprehensive error handling and retry mechanisms
- **Configurable**: Environment-based configuration management

## Installation

### Prerequisites

- Python 3.8 or higher
- pip package manager

### Install from Source

```bash
# Clone the repository
git clone https://github.com/fx-ai/fx-mcp-server.git
cd fx-mcp-server

# Create virtual environment
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate

# Install dependencies
pip install -e .
```

### Install for Development

```bash
# Install with development dependencies
pip install -e ".[dev]"

# Install pre-commit hooks
pre-commit install
```

## Configuration

### Environment Variables

Copy the example environment file and configure your settings:

```bash
cp .env.example .env
```

Edit `.env` with your actual configuration:

```env
# OCR Service Configuration
FX_OCR_AUTHORIZATION=your_ocr_authorization_token_here

# Chinese Character Query Service Configuration
FX_CHARACTER_AUTHORIZATION=your_character_authorization_token_here

# Note Summary Service Configuration
FX_SUMMARY_AUTHORIZATION=your_summary_authorization_token_here
```

### Configuration Options

| Variable | Description | Default |
|----------|-------------|---------|
| `FX_OCR_BASE_URL` | Base URL for OCR service | `https://ivs.chinamobiledevice.com:11443` |
| `FX_OCR_AUTHORIZATION` | Authorization token for OCR service | Required |
| `FX_OCR_TIMEOUT` | Request timeout for OCR service (seconds) | `30` |
| `FX_OCR_MAX_RETRIES` | Maximum retries for OCR service | `3` |
| `FX_CHARACTER_BASE_URL` | Base URL for character query service | `https://ivs.chinamobiledevice.com:11443` |
| `FX_CHARACTER_AUTHORIZATION` | Authorization token for character query service | Required |
| `FX_CHARACTER_TIMEOUT` | Request timeout for character query service (seconds) | `30` |
| `FX_CHARACTER_MAX_RETRIES` | Maximum retries for character query service | `3` |
| `FX_SUMMARY_BASE_URL` | Base URL for summary service | `https://ivs.chinamobiledevice.com:11443` |
| `FX_SUMMARY_AUTHORIZATION` | Authorization token for summary service | Required |
| `FX_SUMMARY_TIMEOUT` | Request timeout for summary service (seconds) | `60` |
| `FX_SUMMARY_MAX_RETRIES` | Maximum retries for summary service | `3` |
| `FX_DEBUG` | Enable debug logging | `false` |

## Usage

### Running the Server

```bash
# Run the MCP server
fx-ai-mcp-server

# Or run directly with Python
python -m fx_ai_mcp.server
```

### Available Tools

#### 1. OCR Image (`ocr_image`)

Extract text from images using optical character recognition.

**Parameters:**
- `image` (string, required): Base64 encoded image data

**Example:**
```json
{
  "name": "ocr_image",
  "arguments": {
    "image": "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
  }
}
```

**Response:**
```
OCR Results:
Total words found: 1

1. Text: '无'
   Location: (1, 4) Size: 40x41
```

#### 2. Query Chinese Character (`query_chinese_character`)

Get detailed information about Chinese characters.

**Parameters:**
- `word` (string, required): Chinese character to query

**Example:**
```json
{
  "name": "query_chinese_character",
  "arguments": {
    "word": "你"
  }
}
```

**Response:**
```
Chinese Character Information: '你'

Structure: 左右结构
Pinyin: nǐ
Definition: 代词，称对方（一个人）；有时也指"你们"
Voice URLs: https://hanyu-word-pinyin-short.cdn.bcebos.com/ni3.mp3
```

#### 3. Summarize Note (`summarize_note`)

Generate intelligent summaries of text content.

**Parameters:**
- `content` (string, required): Content to summarize
- `stream` (boolean, optional): Enable streaming response (default: false)

**Example:**
```json
{
  "name": "summarize_note",
  "arguments": {
    "content": "近日，中国科学院宣布在量子计算领域取得重大突破。研究团队成功研发出新型量子计算芯片，实现了72个量子比特的稳定控制，大幅提升了量子计算的处理能力。该成果发表在国际顶级期刊《Nature》上",
    "stream": false
  }
}
```

**Response:**
```
Note Summary:

**会议纪要**
## [中科院量子计算取得重大突破]
中国科学院研究团队成功研发新型量子计算芯片，实现72个量子比特的稳定控制，大幅提升量子计算处理能力，该成果发表于国际顶级期刊《Nature》。

## 待办事项
**无待办事项**
```

## API Reference

### OCR Service

**Endpoint:** `POST /open-api/ai/v1/ocr`

**Request:**
```json
{
  "image": "base64_encoded_image_data"
}
```

**Response:**
```json
{
  "code": 0,
  "data": {
    "words_result": [
      {
        "words": "recognized_text",
        "location": {
          "top": 4,
          "left": 1,
          "width": 40,
          "height": 41
        }
      }
    ],
    "log_id": "1940606544101598431",
    "words_result_num": 1
  },
  "msg": ""
}
```

### Chinese Character Query Service

**Endpoint:** `POST /device-api/ai/v1/word-query`

**Request:**
```json
{
  "word": "你"
}
```

**Response:**
```json
{
  "code": 0,
  "data": {
    "name": "你",
    "struct": "左右结构",
    "pinyin": ["nǐ"],
    "voice": ["https://hanyu-word-pinyin-short.cdn.bcebos.com/ni3.mp3"],
    "definition": ["代词，称对方（一个人）；有时也指"你们""]
  },
  "msg": ""
}
```

### Note Summary Service

**Endpoint:** `POST /device-api/ai/v1/note-summary`

**Request:**
```json
{
  "content": "text_content_to_summarize",
  "stream": true
}
```

**Streaming Response:**
```
data:{"msg":"","data":{"is_end":"0","content":"**会议"},"logId":"3005659734","status":0}
data:{"msg":"","data":{"is_end":"0","content":"**会议纪要"},"logId":"3005659734","status":0}
```

**Non-streaming Response:**
```json
{
  "msg": "",
  "data": {
    "content": "**会议纪要**\n## [中科院量子计算取得重大突破]\n中国科学院研究团队成功研发新型量子计算芯片，实现72个量子比特的稳定控制，大幅提升量子计算处理能力，该成果发表于国际顶级期刊《Nature》。\n\n## 待办事项\n**无待办事项**"
  },
  "logId": "3946605315",
  "status": 0
}
```

## Development

### Project Structure

```
fx_mcp_server/
├── src/
│   └── fx_ai_mcp/
│       ├── __init__.py          # Package initialization
│       ├── server.py            # Main MCP server implementation
│       ├── config.py            # Configuration management
│       └── models.py            # Pydantic data models
├── examples/                    # Usage examples
├── tests/                       # Test files
├── pyproject.toml              # Project configuration
├── README.md                   # This file
└── .env.example               # Environment variables template
```

### Running Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=fx_ai_mcp

# Run specific test file
pytest tests/test_server.py
```

### Code Quality

```bash
# Format code
black src/ tests/

# Sort imports
isort src/ tests/

# Type checking
mypy src/

# Run all quality checks
pre-commit run --all-files
```

## Examples

### Basic Usage with MCP Client

```python
import asyncio
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

async def main():
    server_params = StdioServerParameters(
        command="fx-ai-mcp-server",
        args=[],
        env=None,
    )

    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            # Initialize the connection
            await session.initialize()

            # List available tools
            tools = await session.list_tools()
            print("Available tools:", [tool.name for tool in tools.tools])

            # Use OCR tool
            result = await session.call_tool(
                "ocr_image",
                arguments={"image": "your_base64_image_here"}
            )
            print("OCR Result:", result.content[0].text)

            # Query Chinese character
            result = await session.call_tool(
                "query_chinese_character",
                arguments={"word": "你"}
            )
            print("Character Info:", result.content[0].text)

            # Summarize note
            result = await session.call_tool(
                "summarize_note",
                arguments={
                    "content": "Your text content here",
                    "stream": False
                }
            )
            print("Summary:", result.content[0].text)

if __name__ == "__main__":
    asyncio.run(main())
```

### Using the HTTP Client Directly

```python
import asyncio
from fx_ai_mcp import FXAIClient

async def main():
    async with FXAIClient() as client:
        # OCR example
        ocr_result = await client.ocr_image("base64_image_data")
        print("OCR:", ocr_result)

        # Character query example
        char_result = await client.query_chinese_character("你")
        print("Character:", char_result)

        # Note summary example
        summary_result = await client.summarize_note("content", stream=False)
        print("Summary:", summary_result)

if __name__ == "__main__":
    asyncio.run(main())
```

## Error Handling

The server includes comprehensive error handling:

- **Network Errors**: Automatic retries with exponential backoff
- **Authentication Errors**: Clear error messages for invalid tokens
- **Validation Errors**: Input validation with detailed error messages
- **Rate Limiting**: Graceful handling of API rate limits
- **Timeout Handling**: Configurable timeouts for all requests

## Troubleshooting

### Common Issues

1. **Authentication Failed**
   - Verify your authorization tokens in the `.env` file
   - Check that tokens have the correct permissions

2. **Connection Timeout**
   - Increase timeout values in configuration
   - Check network connectivity to the API endpoints

3. **Invalid Image Format**
   - Ensure images are properly base64 encoded
   - Verify image format is supported by the OCR service

4. **Character Not Found**
   - Verify the character is a valid Chinese character
   - Some characters may not be in the database

### Debug Mode

Enable debug mode for detailed logging:

```bash
export FX_DEBUG=true
fx-ai-mcp-server
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Add tests for your changes
5. Run the test suite (`pytest`)
6. Commit your changes (`git commit -m 'Add amazing feature'`)
7. Push to the branch (`git push origin feature/amazing-feature`)
8. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

- **Issues**: [GitHub Issues](https://github.com/fx-ai/fx-mcp-server/issues)
- **Email**: <EMAIL>
- **Documentation**: [GitHub README](https://github.com/fx-ai/fx-mcp-server#readme)

## Changelog

### v1.0.0 (2025-07-03)

- Initial release
- OCR service integration
- Chinese character query service
- Note summary service with streaming support
- Full MCP protocol implementation
- Comprehensive error handling and retry logic
- Type-safe implementation with Pydantic models
