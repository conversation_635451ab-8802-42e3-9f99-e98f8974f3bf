#!/usr/bin/env python3
"""
Convenience script to run the FX AI MCP Server.

This script provides an easy way to start the server with proper error handling
and configuration validation.
"""

import sys
import asyncio
import logging
from pathlib import Path

# Add src to path for development
sys.path.insert(0, str(Path(__file__).parent / "src"))

from fx_ai_mcp.server import main
from fx_ai_mcp.config import config


def setup_logging():
    """Set up logging configuration."""
    log_level = logging.DEBUG if config.debug else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )


def check_configuration():
    """Check if the configuration is valid."""
    try:
        config.validate_config()
        print("✓ Configuration validated successfully")
        return True
    except ValueError as e:
        print(f"✗ Configuration error: {e}")
        print("\nPlease check your .env file or environment variables.")
        print("See .env.example for the required configuration.")
        return False


def main_wrapper():
    """Main wrapper function."""
    print("FX AI MCP Server")
    print("================")
    print()
    
    # Set up logging
    setup_logging()
    
    # Check configuration
    if not check_configuration():
        sys.exit(1)
    
    print("Starting server...")
    print("Press Ctrl+C to stop")
    print()
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nServer stopped by user")
    except Exception as e:
        print(f"\nServer error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main_wrapper()
