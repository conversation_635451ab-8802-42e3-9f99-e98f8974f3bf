#!/usr/bin/env python3
"""
Direct client usage example for FX AI services.

This example shows how to use the FXAIClient directly without MCP,
which can be useful for integration into existing applications.
"""

import asyncio
import os
from fx_ai_mcp import FXAIClient


async def main():
    """Main function demonstrating direct client usage."""
    print("FX AI Direct Client Usage Example")
    print("=================================")
    
    # Check if configuration is available
    required_env_vars = [
        "FX_OCR_AUTHORIZATION",
        "FX_CHARACTER_AUTHORIZATION", 
        "FX_SUMMARY_AUTHORIZATION"
    ]
    
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    if missing_vars:
        print(f"Missing required environment variables: {', '.join(missing_vars)}")
        print("Please set these in your .env file or environment.")
        return
    
    try:
        async with FXAIClient() as client:
            # Example 1: OCR Service
            print("\n=== OCR Service Example ===")
            # Simple test image (1x1 pixel PNG)
            test_image = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
            
            try:
                ocr_response = await client.ocr_image(test_image)
                print(f"OCR Response Code: {ocr_response.code}")
                if ocr_response.code == 0 and ocr_response.data:
                    print(f"Words found: {ocr_response.data.get('words_result_num', 0)}")
                    for i, word_result in enumerate(ocr_response.data.get('words_result', []), 1):
                        print(f"  {i}. '{word_result.get('words', '')}' at {word_result.get('location', {})}")
                else:
                    print(f"OCR failed: {ocr_response.msg}")
            except Exception as e:
                print(f"OCR Error: {e}")
            
            # Example 2: Chinese Character Query
            print("\n=== Chinese Character Query Example ===")
            test_characters = ["你", "好", "中", "国"]
            
            for char in test_characters:
                try:
                    char_response = await client.query_chinese_character(char)
                    print(f"\nCharacter: {char}")
                    print(f"Response Code: {char_response.code}")
                    
                    if char_response.code == 0 and char_response.data:
                        data = char_response.data
                        print(f"  Name: {data.name}")
                        print(f"  Structure: {data.struct}")
                        print(f"  Pinyin: {', '.join(data.pinyin)}")
                        print(f"  Definition: {'; '.join(data.definition)}")
                        if data.voice:
                            print(f"  Voice: {data.voice[0]}")
                    else:
                        print(f"  Query failed: {char_response.msg}")
                        
                except Exception as e:
                    print(f"Character query error for '{char}': {e}")
            
            # Example 3: Note Summary Service
            print("\n=== Note Summary Service Example ===")
            sample_text = """
            人工智能技术在近年来取得了显著进展。深度学习、机器学习和自然语言处理等技术
            正在改变我们的生活和工作方式。从智能助手到自动驾驶汽车，AI技术的应用越来越
            广泛。然而，我们也需要关注AI技术带来的伦理和社会问题，确保技术发展能够造福
            全人类。
            """
            
            try:
                # Non-streaming summary
                print("\n--- Non-streaming Summary ---")
                summary_response = await client.summarize_note(sample_text.strip(), stream=False)
                print(f"Response Status: {summary_response.status}")
                
                if summary_response.status == 0 and summary_response.data:
                    print("Summary:")
                    print(summary_response.data.get('content', ''))
                else:
                    print(f"Summary failed: {summary_response.msg}")
                
                # Streaming summary
                print("\n--- Streaming Summary ---")
                streaming_response = await client.summarize_note(sample_text.strip(), stream=True)
                print(f"Response Status: {streaming_response.status}")
                
                if streaming_response.status == 0 and streaming_response.data:
                    print("Streaming Summary:")
                    print(streaming_response.data.get('content', ''))
                else:
                    print(f"Streaming summary failed: {streaming_response.msg}")
                    
            except Exception as e:
                print(f"Summary Error: {e}")
            
            print("\n=== Direct Client Example Complete ===")
            
    except Exception as e:
        print(f"Client Error: {e}")


if __name__ == "__main__":
    asyncio.run(main())
