#!/usr/bin/env python3
"""
Basic usage example for FX AI MCP Server.

This example demonstrates how to use the MCP server with all three services:
- OCR (Optical Character Recognition)
- Chinese Character Query
- Note Summary
"""

import asyncio
import base64
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client


async def main():
    """Main function demonstrating basic usage."""
    # Configure server parameters
    server_params = StdioServerParameters(
        command="fx-ai-mcp-server",
        args=[],
        env=None,
    )
    
    try:
        async with stdio_client(server_params) as (read, write):
            async with ClientSession(read, write) as session:
                # Initialize the connection
                print("Initializing MCP session...")
                await session.initialize()
                
                # List available tools
                print("\n=== Available Tools ===")
                tools = await session.list_tools()
                for tool in tools.tools:
                    print(f"- {tool.name}: {tool.description}")
                
                # Example 1: OCR Image
                print("\n=== OCR Example ===")
                # Create a simple test image (1x1 pixel PNG in base64)
                test_image_b64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
                
                try:
                    ocr_result = await session.call_tool(
                        "ocr_image",
                        arguments={"image": test_image_b64}
                    )
                    print("OCR Result:")
                    print(ocr_result.content[0].text)
                except Exception as e:
                    print(f"OCR Error: {e}")
                
                # Example 2: Chinese Character Query
                print("\n=== Chinese Character Query Example ===")
                characters_to_query = ["你", "好", "世", "界"]
                
                for char in characters_to_query:
                    try:
                        char_result = await session.call_tool(
                            "query_chinese_character",
                            arguments={"word": char}
                        )
                        print(f"\nCharacter '{char}':")
                        print(char_result.content[0].text)
                    except Exception as e:
                        print(f"Character query error for '{char}': {e}")
                
                # Example 3: Note Summary
                print("\n=== Note Summary Example ===")
                sample_content = """
                今天的会议讨论了以下几个重要议题：
                1. 项目进度更新：开发团队报告了当前的进展情况
                2. 预算审查：财务部门提供了最新的预算报告
                3. 市场分析：市场部门分享了竞争对手分析
                4. 下一步计划：确定了未来两周的工作重点
                会议决定下周继续跟进这些议题的执行情况。
                """
                
                try:
                    # Non-streaming summary
                    summary_result = await session.call_tool(
                        "summarize_note",
                        arguments={
                            "content": sample_content.strip(),
                            "stream": False
                        }
                    )
                    print("Summary Result (Non-streaming):")
                    print(summary_result.content[0].text)
                    
                    # Streaming summary
                    print("\n--- Streaming Summary ---")
                    streaming_result = await session.call_tool(
                        "summarize_note",
                        arguments={
                            "content": sample_content.strip(),
                            "stream": True
                        }
                    )
                    print("Summary Result (Streaming):")
                    print(streaming_result.content[0].text)
                    
                except Exception as e:
                    print(f"Summary Error: {e}")
                
                print("\n=== Example Complete ===")
                
    except Exception as e:
        print(f"Connection Error: {e}")
        print("Make sure the FX AI MCP Server is properly configured with valid authorization tokens.")


if __name__ == "__main__":
    print("FX AI MCP Server - Basic Usage Example")
    print("=====================================")
    asyncio.run(main())
